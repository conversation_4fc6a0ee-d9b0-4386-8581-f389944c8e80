export default {
  routes: [
    {
      method: 'POST',
      path: '/referrers/register',
      handler: 'referrer.register',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'GET',
      path: '/admin/affiliate-program/dashboard',
      handler: 'referrer.dashboard',
      config: {
        policies: [], // You can add custom policies if needed
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/referrers/recalculate-metrics',
      handler: 'referrer.recalculateMetrics',
      config: {
        policies: [],
        middlewares: [],
        description: 'Bulk recalculate financial metrics for all referrers',
      },
    },
    {
      method: 'POST',
      path: '/referrers/:id/sync-totals',
      handler: 'referrer.recalculateMetrics',
      config: {
        policies: [],
        middlewares: [],
        description: 'Recalculate financial metrics for a specific referrer',
      },
    },
    {
      method: 'GET',
      path: '/referrers/:id/metrics-summary',
      handler: 'referrer.getMetricsSummary',
      config: {
        policies: [],
        middlewares: [],
        description: 'Get detailed financial metrics summary for a referrer',
      },
    },
  ],
};
