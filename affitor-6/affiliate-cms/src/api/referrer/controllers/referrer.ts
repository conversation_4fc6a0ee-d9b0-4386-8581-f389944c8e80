/**
 * referrer controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::referrer.referrer', ({ strapi }) => ({
  async register(ctx) {
    try {
      // Call the custom service method
      const result = await strapi.service('api::referrer.referrer').register({
        user: ctx.state.user,
      });

      return result;
    } catch (err) {
      ctx.body = err;
      return ctx.badRequest('Registration failed', { error: err.message });
    }
  },

  async dashboard(ctx) {
    try {
      // Get total count of referrers in the system
      const totalReferrers = await strapi.entityService.count('api::referrer.referrer');

      // Get total count of all referrals in the system with referral_status 'conversion'
      const totalReferrals = await strapi.entityService.count('api::referral.referral', {
        filters: { referral_status: 'conversion' },
      });

      // Get all referrer links to calculate total clicks and leads
      const allReferrerLinks = await strapi.entityService.findMany(
        'api::referrer-link.referrer-link',
        {
          fields: ['visitors', 'leads', 'conversions'],
        }
      );

      // Calculate total clicks (visitors) and total leads from all referrer links
      const totalClicks = allReferrerLinks.reduce((sum, link) => sum + (link.visitors || 0), 0);
      const totalLeads = allReferrerLinks.reduce((sum, link) => sum + (link.leads || 0), 0);

      // Get total revenue from all referral commissions in the system
      const allReferralCommissions = await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        {
          fields: ['gross_sale_amount'],
        }
      );

      const totalRevenue = allReferralCommissions.reduce((sum, commission) => {
        const amount = commission.gross_sale_amount;
        if (typeof amount === 'number') {
          return sum + amount;
        } else if (typeof amount === 'string') {
          return sum + (parseFloat(amount) || 0);
        }
        return sum;
      }, 0);

      const recentActivity = await strapi.entityService.findMany(
        'api::referral-activity.referral-activity',
        {
          sort: { createdAt: 'desc' },
          limit: 10,
          populate: {
            referral: {
              populate: {
                user: true,
              },
            },
          },
        }
      );

      const previousMonthRevenue = await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        {
          filters: {
            createdAt: {
              $lt: new Date(new Date().setMonth(new Date().getMonth() - 1)),
            },
          },
          fields: ['gross_sale_amount'],
        }
      );

      const previousMonthRevenueAmount = previousMonthRevenue.reduce((sum, commission) => {
        const amount = commission.gross_sale_amount;
        if (typeof amount === 'number') {
          return sum + amount;
        } else if (typeof amount === 'string') {
          return sum + (parseFloat(amount) || 0);
        }
        return sum;
      }, 0);

      const currentMonthRevenue = totalRevenue - previousMonthRevenueAmount;
      const growthRate =
        previousMonthRevenueAmount > 0
          ? ((currentMonthRevenue - previousMonthRevenueAmount) / previousMonthRevenueAmount) * 100
          : currentMonthRevenue > 0
            ? 100
            : 0;

      const topReferrers = await strapi.entityService.findMany('api::referrer.referrer', {
        sort: { total_earnings: 'desc' },
        limit: 10,
        populate: {
          user: {
            fields: ['username', 'email'],
          },
        },
      });

      const topReferrals = await strapi.entityService.findMany('api::referral.referral', {
        sort: { createdAt: 'desc' },
        limit: 10,
        populate: {
          user: {
            fields: ['username', 'email'],
          },
        },
      });

      return {
        success: true,
        data: {
          totalReferrers,
          totalReferrals,
          totalClicks,
          totalLeads,
          totalRevenue: parseFloat(totalRevenue.toFixed(2)),
          recentActivity,
          topReferrers,
          topReferrals,
          previousMonthRevenueAmount: parseFloat(previousMonthRevenueAmount.toFixed(2)),
          currentMonthRevenue: parseFloat(currentMonthRevenue.toFixed(2)),
          growthRate: parseFloat(growthRate.toFixed(2)),
        },
      };
    } catch (err) {
      ctx.body = err;
      return ctx.badRequest('Dashboard data retrieval failed', { error: err.message });
    }
  },

  /**
   * Recalculate financial metrics for a specific referrer or all referrers
   * POST /api/referrers/recalculate-metrics
   * POST /api/referrers/:id/sync-totals
   */
  async recalculateMetrics(ctx) {
    try {
      const { id: referrerId } = ctx.params;
      const { dateFrom, dateTo, referrerIds } = ctx.request.body || {};

      // Validate date formats if provided
      if (dateFrom && isNaN(Date.parse(dateFrom))) {
        return ctx.badRequest('Invalid dateFrom format. Use ISO date string (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss.sssZ)');
      }
      if (dateTo && isNaN(Date.parse(dateTo))) {
        return ctx.badRequest('Invalid dateTo format. Use ISO date string (YYYY-MM-DD or YYYY-MM-DDTHH:mm:ss.sssZ)');
      }

      const referrerService = strapi.service('api::referrer.referrer');

      // Single referrer recalculation
      if (referrerId) {
        const result = await referrerService.recalculateReferrerMetrics(referrerId, {
          dateFrom,
          dateTo,
        });

        return ctx.send({
          success: true,
          message: `Financial metrics recalculated successfully for referrer ${referrerId}`,
          data: result,
        });
      }

      // Bulk recalculation
      const result = await referrerService.bulkRecalculateMetrics({
        dateFrom,
        dateTo,
        referrerIds,
      });

      return ctx.send({
        success: true,
        message: result.message,
        data: result,
      });
    } catch (err) {
      strapi.log.error('Error in recalculateMetrics:', err);

      if (err.message.includes('not found')) {
        return ctx.notFound(err.message);
      }

      return ctx.badRequest('Failed to recalculate financial metrics', {
        error: err.message,
        timestamp: new Date().toISOString(),
      });
    }
  },

  /**
   * Get financial metrics summary for a referrer
   * GET /api/referrers/:id/metrics-summary
   */
  async getMetricsSummary(ctx) {
    try {
      const { id: referrerId } = ctx.params;
      const { dateFrom, dateTo } = ctx.query;

      // Validate referrer exists
      const referrer = await strapi.entityService.findOne('api::referrer.referrer', referrerId, {
        fields: ['id', 'total_revenue', 'total_earnings', 'balance'],
        populate: {
          user: {
            fields: ['username', 'email'],
          },
        },
      });

      if (!referrer) {
        return ctx.notFound(`Referrer with ID ${referrerId} not found`);
      }

      // Build filters for commission query
      const filters = { referrer: referrerId };
      if (dateFrom || dateTo) {
        filters.createdAt = {};
        if (dateFrom) filters.createdAt.$gte = new Date(dateFrom);
        if (dateTo) filters.createdAt.$lte = new Date(dateTo);
      }

      // Get commission statistics
      const commissions = await strapi.entityService.findMany(
        'api::referral-commission.referral-commission',
        {
          filters,
          fields: ['commission_amount', 'gross_sale_amount', 'commission_status', 'createdAt'],
          pagination: { limit: -1 },
        }
      );

      // Calculate detailed metrics
      const metrics = {
        totalCommissions: commissions.length,
        statusBreakdown: {
          pending: 0,
          ready: 0,
          paid: 0,
        },
        financialSummary: {
          totalRevenue: 0,
          totalEarnings: 0,
          averageCommissionRate: 0,
        },
        dateRange: {
          from: dateFrom || null,
          to: dateTo || null,
        },
      };

      commissions.forEach((commission) => {
        // Status breakdown
        const status = commission.commission_status || 'pending';
        if (metrics.statusBreakdown.hasOwnProperty(status)) {
          metrics.statusBreakdown[status]++;
        }

        // Financial calculations
        const grossAmount = parseFloat(commission.gross_sale_amount) || 0;
        const commissionAmount = parseFloat(commission.commission_amount) || 0;

        metrics.financialSummary.totalRevenue += grossAmount;
        metrics.financialSummary.totalEarnings += commissionAmount;
      });

      // Calculate average commission rate
      if (metrics.financialSummary.totalRevenue > 0) {
        metrics.financialSummary.averageCommissionRate =
          (metrics.financialSummary.totalEarnings / metrics.financialSummary.totalRevenue) * 100;
      }

      // Round financial values
      metrics.financialSummary.totalRevenue = Math.round(metrics.financialSummary.totalRevenue * 100) / 100;
      metrics.financialSummary.totalEarnings = Math.round(metrics.financialSummary.totalEarnings * 100) / 100;
      metrics.financialSummary.averageCommissionRate = Math.round(metrics.financialSummary.averageCommissionRate * 100) / 100;

      return ctx.send({
        success: true,
        data: {
          referrer: {
            id: referrer.id,
            username: referrer.user?.username,
            email: referrer.user?.email,
            currentTotals: {
              total_revenue: referrer.total_revenue || 0,
              total_earnings: referrer.total_earnings || 0,
              balance: referrer.balance || 0,
            },
          },
          metrics,
          timestamp: new Date().toISOString(),
        },
      });
    } catch (err) {
      strapi.log.error('Error in getMetricsSummary:', err);
      return ctx.badRequest('Failed to retrieve metrics summary', {
        error: err.message,
        timestamp: new Date().toISOString(),
      });
    }
  },
}));
